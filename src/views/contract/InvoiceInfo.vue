<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useToast } from "primevue/usetoast";
import type {
  InvoiceInfoItem,
  InvoiceFormData,
  InvoiceSearchParams,
} from "../../types/invoice";
import {
  InvoiceType,
  CustomerVerifyType,
  CustomerFareOrder,
  IsUnusualNeed,
  IsOpenCharge,
  PostalType,
} from "../../types/invoice";
import {
  getInvoiceList,
  createInvoice,
  updateInvoice,
  getInvoiceDetail,
} from "../../services/invoice";
import { useRoute } from "vue-router";
import { getContractsSimpleList } from "../../services/contract";

const toast = useToast();
const route = useRoute();
const loading = ref(false);
const totalRecords = ref(0);
const submitted = ref(false);

const hasOperationPermission = computed(() => {
  const currentRoute = route.matched[route.matched.length - 1];
  return currentRoute?.meta?.operation ?? false;
});

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});
// 筛选选项
const filterColumnOptions = [
  { label: "--", value: "--" },
  { label: "创建者", value: "create_user" },
  { label: "客户开票名称", value: "customer_invoice_name" },
];
const selectedFilterColumn = ref("--");
const filterValue = ref("");
const filterContractNum = ref("");

// 表单数据
const invoiceDrawerVisible = ref(false);
const invoiceDetailDialog = ref(false);
const invoices = ref<InvoiceInfoItem[]>([]);
const selectedInvoice = ref<InvoiceInfoItem | null>(null);
const invoiceForm = ref<InvoiceFormData>({
  contract_num: "",
  customer_invoice_name: "",
  customer_deposit_bank: "",
  customer_deposit_bank_sub: "",
  customer_bank_account_name: "",
  customer_bank_account: "",
  customer_tax_number: "",
  customer_invoice_type: InvoiceType.VAT_SPECIAL_TICKET,
  customer_receive_explain: "",
  customer_verify_type: CustomerVerifyType.NO_VERIFY,
  customer_fare_order: CustomerFareOrder.TICKET_FIRST,
  is_unusual_need: IsUnusualNeed.NO,
  unusual_need_explain: "",
  is_open_charge: IsOpenCharge.SUSPENDED,
  postal_type: PostalType.EMAIL,
  postal_address: "",
  charge_start_day: new Date(),
  charge_end_day: new Date(),
});

// 加载合同简单列表
const contractOptions = ref<
  { label: string; value: string; customer_id: number }[]
>([]);
const loadContractOptions = async () => {
  try {
    const response = await getContractsSimpleList();
    contractOptions.value = response.data.map((item) => ({
      label: `${item.contract_title} (${item.contract_num})`,
      value: item.contract_num,
      customer_id: item.customer_id,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载合同列表失败",
      life: 3000,
    });
  }
};

// 下拉选项
const invoiceTypeOptions = [
  { label: "增值税专票", value: InvoiceType.VAT_SPECIAL_TICKET },
  { label: "增值税普票", value: InvoiceType.VAT_GENERAL_TICKET },
  { label: "invoice", value: InvoiceType.INVOICE },
];

const customerVerifyTypeOptions = [
  { label: "不对账", value: CustomerVerifyType.NO_VERIFY },
  { label: "对账无需确认", value: CustomerVerifyType.VERIFY_NO_CONFIRM },
  {
    label: "对账超期无需确认",
    value: CustomerVerifyType.VERIFY_EXPIRED_NO_CONFIRM,
  },
  { label: "对账要确认", value: CustomerVerifyType.VERIFY_NEED_CONFIRM },
];

const customerFareOrderOptions = [
  { label: "先票后款", value: CustomerFareOrder.TICKET_FIRST },
  { label: "先款后票", value: CustomerFareOrder.PAYMENT_FIRST },
];

const isUnusualNeedOptions = [
  { label: "是", value: IsUnusualNeed.YES },
  { label: "否", value: IsUnusualNeed.NO },
];

const isOpenChargeOptions = [
  { label: "暂停", value: IsOpenCharge.SUSPENDED },
  { label: "开账", value: IsOpenCharge.ACTIVE },
  { label: "终止", value: IsOpenCharge.TERMINATED },
];

const postalTypeOptions = [
  { label: "邮件", value: PostalType.EMAIL },
  { label: "快递", value: PostalType.EXPRESS },
  { label: "邮件且快递", value: PostalType.EMAIL_AND_EXPRESS },
  { label: "无需", value: PostalType.NONE },
];

const invoiceStatusSeverityMap: Record<string, string> = {
  暂存: "info",
  注销: "danger",
  审核生效: "success",
  待账务审核: "warn",
};

// 加载发票列表
const loadInvoices = async () => {
  try {
    loading.value = true;
    const params: InvoiceSearchParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };
    if (filterValue.value && selectedFilterColumn.value) {
      params.filter = filterValue.value;
      params.filterColumn = selectedFilterColumn.value;
    }
    if (filterContractNum.value) {
      params.contract_num = filterContractNum.value;
    }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key as keyof InvoiceSearchParams] === "") {
        delete params[key as keyof InvoiceSearchParams];
      }
    });

    const response = await getInvoiceList(params);
    if (response.code === 200) {
      invoices.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "加载发票列表失败",
        life: 4000,
      });
    }
  } catch (error) {
    console.error("Failed to load invoice list:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票列表失败",
      life: 4000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadInvoices();
};

// 搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadInvoices();
};

// 打开新增发票对话框
const openNew = () => {
  invoiceForm.value = {
    contract_num: "",
    customer_invoice_name: "",
    customer_deposit_bank: "",
    customer_deposit_bank_sub: "",
    customer_bank_account_name: "",
    customer_bank_account: "",
    customer_tax_number: "",
    customer_invoice_type: InvoiceType.VAT_SPECIAL_TICKET,
    customer_receive_explain: "",
    customer_verify_type: CustomerVerifyType.NO_VERIFY,
    customer_fare_order: CustomerFareOrder.TICKET_FIRST,
    is_unusual_need: IsUnusualNeed.NO,
    unusual_need_explain: "",
    is_open_charge: IsOpenCharge.SUSPENDED,
    postal_type: PostalType.EMAIL,
    postal_address: "",
    charge_start_day: new Date(),
    charge_end_day: new Date(),
  };
  submitted.value = false;
  invoiceDrawerVisible.value = true;
};

// 编辑发票
const editInvoice = (invoice: InvoiceInfoItem) => {
  invoiceForm.value = { ...invoice };
  submitted.value = false;
  invoiceDrawerVisible.value = true;
};

// 查看发票详情
const viewInvoice = async (invoice: InvoiceInfoItem) => {
  try {
    const response = await getInvoiceDetail(invoice.id!);
    if (response.code === 200) {
      selectedInvoice.value = response.data;
      invoiceDetailDialog.value = true;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "获取发票详情失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("Failed to load invoice detail:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取发票详情失败",
      life: 3000,
    });
  }
};

// 处理邮递方式变更
const handlePostalTypeChange = (event: { value: string }) => {
  if (event.value === PostalType.NONE) {
    invoiceForm.value.postal_address = "/";
  }
};

// 保存发票
const saveInvoice = async () => {
  submitted.value = true;

  // 验证日期范围
  if (invoiceForm.value.charge_start_day && invoiceForm.value.charge_end_day) {
    if (
      new Date(invoiceForm.value.charge_start_day) >
      new Date(invoiceForm.value.charge_end_day)
    ) {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "开账开始日期不能晚于结束日期",
        life: 4000,
      });
      return;
    }
  }

  // 将iso格式的日期转换为字符串
  let dateFields = ["charge_start_day", "charge_end_day"];
  dateFields.forEach((field) => {
    (invoiceForm.value as any)[field] = invoiceForm.value[
      field as keyof typeof invoiceForm.value
    ]
      ? new Date(
          invoiceForm.value[field as keyof typeof invoiceForm.value] as
            | string
            | number
            | Date
        )
          .toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .replace(/\//g, "-")
      : "";
  });

  try {
    let response;
    if (invoiceForm.value.id) {
      response = await updateInvoice(invoiceForm.value.id, invoiceForm.value);
    } else {
      response = await createInvoice(invoiceForm.value);
    }

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: invoiceForm.value.id ? "更新发票信息成功" : "创建发票信息成功",
        life: 3000,
      });
      invoiceDrawerVisible.value = false;
      loadInvoices();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "保存发票信息失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("Failed to save invoice:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "保存发票信息失败",
      life: 3000,
    });
  }
};

// 格式化日期
const formatDate = (timestamp: string | undefined) => {
  if (!timestamp) return "";
  const date = new Date(Number(timestamp) * 1000);
  return date.toLocaleString();
};

// 初始加载
onMounted(() => {
  loadInvoices();
  loadContractOptions();
});
</script>

<template>
  <div class="invoice-container">
    <div class="card">
      <div class="card-header">
        <Message variant="simple" size="large">发票信息</Message>
        <div class="header-actions">
          <Button
            label="新建发票信息"
            icon="pi pi-plus"
            class="mr-2"
            :disabled="!hasOperationPermission"
            @click="openNew"
          />
        </div>
      </div>

      <Toolbar class="mb-2">
        <template #start>
          <Select
            v-model="filterContractNum"
            :options="contractOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="请选择合同"
            :showClear="true"
            filter
            @change="loadInvoices"
            class="w-100"
          />
        </template>
        <template #end>
          <FloatLabel class="mr-2">
            <Select
              v-model="selectedFilterColumn"
              :options="filterColumnOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="请选择筛选字段"
              style="width: 15rem"
              size="normal"
            />
          </FloatLabel>
          <FloatLabel class="mr-2">
            <InputText v-model="filterValue" />
            <label>搜索值</label>
          </FloatLabel>
          <Button label="搜索" icon="pi pi-search" @click="handleSearch" />
        </template>
      </Toolbar>

      <DataTable
        :value="invoices"
        :loading="loading"
        :lazy="true"
        :paginator="true"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        @page="onPage($event)"
        showGridlines
        stripedRows
        scrollable
        scrollHeight="calc(100vh - 25rem)"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无发票数据</p>
          </div>
        </template>
        <Column
          field="contract_title"
          header="合同名称"
          style="min-width: 15rem"
        />
        <Column
          field="customer_invoice_name"
          header="开票名称"
          style="min-width: 20rem"
        />
        <Column
          field="account_seq"
          header="分账序号"
          style="min-width: 10rem"
        />
        <Column field="status" header="状态" style="min-width: 8rem">
          <template #body="{ data }">
            <Tag
              :severity="invoiceStatusSeverityMap[data.status] || 'info'"
              :value="data.status"
            />
          </template>
        </Column>
        <Column field="create_user" header="创建者" style="min-width: 8rem" />
        <Column field="created_at" header="创建时间" style="min-width: 20rem">
          <template #body="{ data }">
            {{ formatDate(data.created_at) }}
          </template>
        </Column>
        <Column
          header="操作"
          :exportable="false"
          alignFrozen="right"
          frozen
          style="min-width: 15rem"
        >
          <template #body="{ data }">
            <Button
              icon="pi pi-pencil"
              outlined
              class="p-button-rounded p-button-success mr-2"
              @click="editInvoice(data)"
              v-tooltip.top="'编辑发票信息'"
            />
            <Button
              icon="pi pi-eye"
              outlined
              class="p-button-rounded p-button-info"
              @click="viewInvoice(data)"
              v-tooltip.top="'查看发票信息详情'"
            />
          </template>
        </Column>
      </DataTable>

      <Drawer
        v-model:visible="invoiceDrawerVisible"
        position="right"
        :style="{ width: '70rem' }"
        :modal="true"
        :closable="true"
        :dismissable="false"
        :showCloseIcon="true"
        :header="invoiceForm.id ? '编辑发票信息' : '新建发票信息'"
      >
        <Fluid>
          <div class="grid grid-cols-2 gap-2">
            <div class="p-field">
              <label for="contract_num" class="required">合同编号</label>
              <Select
                id="contract_num"
                v-model="invoiceForm.contract_num"
                :options="contractOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="选择关联合同"
                filter
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.contract_num"
                >合同编号不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_invoice_name" class="required"
                >开票名称</label
              >
              <InputText
                id="customer_invoice_name"
                v-model="invoiceForm.customer_invoice_name"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.customer_invoice_name,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_invoice_name"
                >开票名称不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_deposit_bank" class="required"
                >开户银行</label
              >
              <InputText
                id="customer_deposit_bank"
                v-model="invoiceForm.customer_deposit_bank"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.customer_deposit_bank,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_deposit_bank"
                >开户银行不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_deposit_bank_sub" class="required"
                >开户支行</label
              >
              <InputText
                id="customer_deposit_bank_sub"
                v-model="invoiceForm.customer_deposit_bank_sub"
                required
                :class="{
                  'p-invalid':
                    submitted && !invoiceForm.customer_deposit_bank_sub,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_deposit_bank_sub"
                >开户支行不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_bank_account_name" class="required"
                >银行账户名</label
              >
              <InputText
                id="customer_bank_account_name"
                v-model="invoiceForm.customer_bank_account_name"
                required
                :class="{
                  'p-invalid':
                    submitted && !invoiceForm.customer_bank_account_name,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_bank_account_name"
                >银行账户名不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_bank_account" class="required"
                >银行账号</label
              >
              <InputText
                id="customer_bank_account"
                v-model="invoiceForm.customer_bank_account"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.customer_bank_account,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_bank_account"
                >银行账号不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_tax_number" class="required">税号</label>
              <InputText
                id="customer_tax_number"
                v-model="invoiceForm.customer_tax_number"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.customer_tax_number,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_tax_number"
                >税号不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_invoice_type" class="required"
                >发票类型</label
              >
              <Select
                id="customer_invoice_type"
                v-model="invoiceForm.customer_invoice_type"
                :options="invoiceTypeOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="请选择发票类型"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.customer_invoice_type,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_invoice_type"
                >发票类型不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_receive_explain">收款说明</label>
              <Textarea
                id="customer_receive_explain"
                v-model="invoiceForm.customer_receive_explain"
                rows="3"
                autoResize
              />
            </div>

            <div class="p-field">
              <label for="customer_verify_type" class="required"
                >对账类型</label
              >
              <Select
                id="customer_verify_type"
                v-model="invoiceForm.customer_verify_type"
                :options="customerVerifyTypeOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="请选择对账类型"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.customer_verify_type,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_verify_type"
                >对账类型不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="customer_fare_order" class="required">票款先后</label>
              <Select
                id="customer_fare_order"
                v-model="invoiceForm.customer_fare_order"
                :options="customerFareOrderOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="请选择票款先后"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.customer_fare_order,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.customer_fare_order"
                >票款先后不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="is_unusual_need" class="required">特殊开账要求</label>
              <Select
                id="is_unusual_need"
                v-model="invoiceForm.is_unusual_need"
                :options="isUnusualNeedOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="请选择是否特殊开账要求"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.is_unusual_need,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.is_unusual_need"
              >
                特殊开账要求不能为空
              </small>
            </div>

            <div class="p-field">
              <label for="unusual_need_explain">特殊开账说明</label>
              <Textarea
                id="unusual_need_explain"
                v-model="invoiceForm.unusual_need_explain"
                rows="3"
                autoResize
                required
                disabled
              />
            </div>

            <div class="p-field">
              <label for="is_open_charge" class="required">开账状态</label>
              <Select
                id="is_open_charge"
                v-model="invoiceForm.is_open_charge"
                :options="isOpenChargeOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="请选择开账状态"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.is_open_charge,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.is_open_charge"
                >开账状态不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="postal_type" class="required">邮递方式</label>
              <Select
                id="postal_type"
                v-model="invoiceForm.postal_type"
                :options="postalTypeOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="请选择邮递方式"
                required
                :class="{ 'p-invalid': submitted && !invoiceForm.postal_type }"
                @change="handlePostalTypeChange"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.postal_type"
                >邮递方式不能为空</small
              >
            </div>

            <div class="p-field" v-if="invoiceForm.postal_type !== '无需'">
              <label for="postal_address">邮寄地址</label>
              <InputText
                id="postal_address"
                v-model="invoiceForm.postal_address"
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.postal_address,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.postal_address"
                >邮寄地址不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="charge_start_day" class="required"
                >开账开始日期</label
              >
              <DatePicker
                id="charge_start_day"
                v-model="invoiceForm.charge_start_day"
                dateFormat="yy-mm-dd"
                showIcon
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.charge_start_day,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.charge_start_day"
                >开账开始日期不能为空</small
              >
            </div>

            <div class="p-field">
              <label for="charge_end_day" class="required">开账结束日期</label>
              <DatePicker
                id="charge_end_day"
                v-model="invoiceForm.charge_end_day"
                dateFormat="yy-mm-dd"
                showIcon
                required
                :class="{
                  'p-invalid': submitted && !invoiceForm.charge_end_day,
                }"
              />
              <small
                class="p-error"
                v-if="submitted && !invoiceForm.charge_end_day"
                >开账结束日期不能为空</small
              >
            </div>
          </div>
        </Fluid>
        <template #footer>
          <Button
            label="取消"
            icon="pi pi-times"
            severity="secondary"
            class="p-button-text mr-2"
            @click="invoiceDrawerVisible = false"
          />
          <Button label="保存" icon="pi pi-check" @click="saveInvoice" />
        </template>
      </Drawer>

      <Dialog
        v-model:visible="invoiceDetailDialog"
        :modal="true"
        maximizable
        :style="{ width: '80rem' }"
        header="发票信息详情"
      >
        <Fluid>
          <div class="grid grid-cols-3 gap-5">
            <div class="p-datatable-filter-rule">
              <label>合同编号:</label>
              <div>{{ selectedInvoice?.contract_num }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>开票名称:</label>
              <div>{{ selectedInvoice?.customer_invoice_name }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>开户银行:</label>
              <div>{{ selectedInvoice?.customer_deposit_bank }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>开户支行:</label>
              <div>{{ selectedInvoice?.customer_deposit_bank_sub }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>银行账户名:</label>
              <div>{{ selectedInvoice?.customer_bank_account_name }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>银行账号:</label>
              <div>{{ selectedInvoice?.customer_bank_account }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>税号:</label>
              <div>{{ selectedInvoice?.customer_tax_number }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>发票类型:</label>
              <div>{{ selectedInvoice?.customer_invoice_type }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>收款说明:</label>
              <div>{{ selectedInvoice?.customer_receive_explain }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>分账序号:</label>
              <div>{{ selectedInvoice?.account_seq }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>分账子序号:</label>
              <div>{{ selectedInvoice?.sub_account_seq }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>对账类型:</label>
              <div>{{ selectedInvoice?.customer_verify_type }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>票款先后:</label>
              <div>{{ selectedInvoice?.customer_fare_order }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>特殊开账要求:</label>
              <div>{{ selectedInvoice?.is_unusual_need }}</div>
            </div>
            <div
              class="p-datatable-filter-rule"
              v-if="selectedInvoice?.is_unusual_need === '是'"
            >
              <label>特殊开账说明:</label>
              <div>{{ selectedInvoice?.unusual_need_explain }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>开账状态:</label>
              <div>{{ selectedInvoice?.is_open_charge }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>邮递方式:</label>
              <div>{{ selectedInvoice?.postal_type }}</div>
            </div>
            <div
              class="p-datatable-filter-rule"
              v-if="selectedInvoice?.postal_type !== '无需'"
            >
              <label>邮寄地址:</label>
              <div>{{ selectedInvoice?.postal_address }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>开账开始日期:</label>
              <div>{{ selectedInvoice?.charge_start_day }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>开账结束日期:</label>
              <div>{{ selectedInvoice?.charge_end_day }}</div>
            </div>
            <div class="p-datatable-filter-rule">
              <label>状态:</label>
              <div>{{ selectedInvoice?.status }}</div>
            </div>
            <div class="border-b border-surface-200 pb-2 flex flex-col gap-2">
              <label>创建者:</label>
              <div>{{ selectedInvoice?.create_user }}</div>
            </div>
          </div>
        </Fluid>
      </Dialog>
    </div>
  </div>
</template>

<style scoped>
.invoice-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.p-field {
  margin-bottom: 1rem;
}

.p-field label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
  color: var(--p-primary-color);
}

.p-field label.required::after {
  content: " *";
  color: var(--p-red-500);
}

.p-fluid label {
  font-weight: 600;
  color: var(--p-primary-color);
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}
</style>
