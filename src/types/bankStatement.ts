export interface BankStatementItem {
  id: number;
  amount: string;
  currency_type: string;
  statement_no: string;
  statement_date: string;
  payment_name: string;
  payment_bank_no: string;
  payment_bank_name: string;
  receive_bank_name: string;
  receive_bank_no: string;
  confirming_amount: string;
  confirmed_amount: string;
  customer_num: string | null;
  customer_name: string | null;
  description: string;
  receipt_status: number;
  created_at: string;
}

export interface BankStatementFormData {
  amount: number | null;
  currency_type: string;
  statement_no: string;
  statement_date: Date | null;
  payment_name: string;
  payment_bank_no: string;
  payment_bank_name: string;
  receive_bank_name: string;
  receive_bank_no: string;
  confirming_amount: number | null;
  confirmed_amount: number | null;
  customer_num: string;
  customer_name: string;
  description: string;
}

export interface BankStatementUpdateFormData {
  customer_num: string;
  customer_name: string;
}

export interface BankStatementParams {
  page?: number;
  pageSize?: number;
  receipt_status?: number;
  statement_no?: string;
  payment_name?: string;
}

// 已认款记录
export interface ReceiveStatementItem {
  id: number;
  bank_statement_id: number;
  bank_statement_no: string;
  created_at: string;
}

// 待认款记录
export interface RecognitionDetailItem {
  id: number;
  account_seq: string;
  order_no: string;
  sub_order_no: string,
  pay_type: string,
  charge_month: string;
  adjust_month: string;
  fee_amount: string;
  unpay_amount: string;
  pay_amount: string;
  income_type: string;
  tax_type: string;
  customer_name: string;
  currency_type: string;
  confirming_amount: string;
}

// 待认款查询参数
export interface RecognitionDetailParams {
  page?: number;
  pageSize?: number;
  account_seq?: string;
  adjust_month?: string;
  charge_month?: string;
  order_no?: string;
}

// 认款请求数据
export interface RecognitionRequestItem {
  amount: string;
  sub_order_no: string;
  income_charge_detail_id: number;
}

export interface RecognitionRequest {
  receive_statement: RecognitionRequestItem[];
}

// 未认款记录
export interface UnrecognizedStatementItem {
  id: number;
  amount: string;
  sub_order_no: string;
  receive_type: string;
  group_approve_state: number;
  created_at: string;
}

// 未认款批量操作请求
export interface UnrecognizedBatchRequest {
  tmp_ids: number[];
}

// 预付费认款记录
export interface PrepaidStatementItem {
  order_num: string;
  total_num: string;
  income_type: string;
  account_seq: string;
  bill_status: string;
}

// 预付费认款请求项
export interface PrepaidRecognitionRequestItem {
  amount: string;
  sub_order_no: string;
}

// 预付费认款请求
export interface PrepaidRecognitionRequest {
  prepaid: PrepaidRecognitionRequestItem[];
}
