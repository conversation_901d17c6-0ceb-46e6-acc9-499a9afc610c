import { ApiListResponse, ApiResponse } from "../types/api";
import {
  BankStatementItem,
  BankStatementUpdateFormData,
  BankStatementParams,
  ReceiveStatementItem,
  RecognitionDetailItem,
  RecognitionDetailParams,
  RecognitionRequest,
  UnrecognizedStatementItem,
  UnrecognizedBatchRequest,
} from "../types/bankStatement";
import api from "./api";

// 获取银行流水列表
export const getBankStatements = async (
  params: BankStatementParams
): Promise<ApiListResponse<BankStatementItem[]>> => {
  const response = await api.get("/bank-statements", { params });
  return response.data;
};

// 更新银行流水
export const updateBankStatement = async (
  id: number,
  data: BankStatementUpdateFormData
): Promise<ApiResponse<any>> => {
  const response = await api.put(`/bank-statements/${id}`, data);
  return response.data;
};

// 获取已认款列表
export const getReceiveStatements = async (
  bankStatementId: number,
  params: { page?: number; pageSize?: number }
): Promise<ApiListResponse<ReceiveStatementItem[]>> => {
  const response = await api.get(`/bank-statements/${bankStatementId}/receive-statements`, { params });
  return response.data;
};

// 获取待认款列表
export const getRecognitionDetails = async (
  bankStatementId: number,
  params: RecognitionDetailParams
): Promise<ApiListResponse<RecognitionDetailItem[]>> => {
  const response = await api.get(`/bank-statements/${bankStatementId}/recognition-details`, { params });
  return response.data;
};

// 获取单个银行流水详情
export const getBankStatementDetail = async (
  id: number
): Promise<ApiResponse<BankStatementItem>> => {
  const response = await api.get(`/bank-statements/${id}`);
  return response.data;
};

// 提交认款
export const submitRecognition = async (
  bankStatementId: number,
  data: RecognitionRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post(`/bank-statements/${bankStatementId}/recognition-details/recognition`, data);
  return response.data;
};

// 获取未认款列表
export const getUnrecognizedStatements = async (
  bankStatementId: number,
  params: { page?: number; pageSize?: number; state?: number }
): Promise<ApiListResponse<UnrecognizedStatementItem[]>> => {
  const response = await api.get(`/bank-statements/${bankStatementId}/receive-statement-tmp`, { params });
  return response.data;
};

// 批量提交审批
export const submitUnrecognizedApproval = async (
  bankStatementId: number,
  data: UnrecognizedBatchRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post(`/bank-statements/${bankStatementId}/receive-statement-tmp/approve`, data);
  return response.data;
};

// 批量撤销
export const revokeUnrecognizedStatements = async (
  bankStatementId: number,
  data: UnrecognizedBatchRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post(`/bank-statements/${bankStatementId}/receive-statement-tmp/revoked`, data);
  return response.data;
};
